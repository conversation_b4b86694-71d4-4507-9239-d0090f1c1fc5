"""
数据库模型定义
包含用户、对话、版本、消息等表的SQLAlchemy模型
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import json

db = SQLAlchemy()

class User(db.Model):
    """用户表"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=True, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    display_name = db.Column(db.String(100), nullable=True)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # 关系
    conversations = db.relationship('Conversation', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'display_name': self.display_name,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class Conversation(db.Model):
    """对话表"""
    __tablename__ = 'conversations'
    
    id = db.Column(db.Integer, primary_key=True)
    conversation_id = db.Column(db.String(50), unique=True, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # 关系
    form_versions = db.relationship('FormVersion', backref='conversation', lazy='dynamic', cascade='all, delete-orphan')
    chat_messages = db.relationship('ChatMessage', backref='conversation', lazy='dynamic', cascade='all, delete-orphan')
    
    def to_dict(self, include_versions=True, include_messages=True):
        """转换为字典"""
        result = {
            'conversation_id': self.conversation_id,
            'title': self.title,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active
        }
        
        if include_versions:
            result['form_versions'] = [v.to_dict() for v in self.form_versions.order_by(FormVersion.created_at.desc())]
        
        if include_messages:
            result['chat_history'] = [m.to_dict() for m in self.chat_messages.order_by(ChatMessage.created_at)]
        
        return result

class FormVersion(db.Model):
    """表单版本表"""
    __tablename__ = 'form_versions'
    
    id = db.Column(db.Integer, primary_key=True)
    version_id = db.Column(db.String(50), unique=True, nullable=False, index=True)
    conversation_id = db.Column(db.Integer, db.ForeignKey('conversations.id'), nullable=False, index=True)
    version_name = db.Column(db.String(200), nullable=False)
    form_data = db.Column(db.Text, nullable=False)  # JSON格式存储表单数据
    created_by = db.Column(db.String(50), nullable=False, default='user')
    modification_notes = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def get_form_data(self):
        """获取表单数据（JSON解析）"""
        try:
            return json.loads(self.form_data) if self.form_data else {}
        except json.JSONDecodeError:
            return {}
    
    def set_form_data(self, data):
        """设置表单数据（JSON序列化）"""
        self.form_data = json.dumps(data, ensure_ascii=False) if data else '{}'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'version_id': self.version_id,
            'version_name': self.version_name,
            'form_data': self.get_form_data(),
            'created_by': self.created_by,
            'modification_notes': self.modification_notes,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class ChatMessage(db.Model):
    """聊天消息表"""
    __tablename__ = 'chat_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.String(50), unique=True, nullable=False, index=True)
    conversation_id = db.Column(db.Integer, db.ForeignKey('conversations.id'), nullable=False, index=True)
    sender = db.Column(db.String(20), nullable=False)  # 'user' 或 'ai'
    message = db.Column(db.Text, nullable=False)
    version_before = db.Column(db.String(50), nullable=True)  # 消息前的版本ID
    version_after = db.Column(db.String(50), nullable=True)   # 消息后的版本ID
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'message_id': self.message_id,
            'sender': self.sender,
            'message': self.message,
            'version_before': self.version_before,
            'version_after': self.version_after,
            'timestamp': self.created_at.isoformat() if self.created_at else None
        }

class UserSession(db.Model):
    """用户会话表"""
    __tablename__ = 'user_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    session_token = db.Column(db.String(255), unique=True, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    user_agent = db.Column(db.String(500), nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)
    
    # 关系
    user = db.relationship('User', backref='sessions')
    
    def is_expired(self):
        """检查会话是否过期"""
        return datetime.utcnow() > self.expires_at
    
    def to_dict(self):
        """转换为字典"""
        return {
            'session_token': self.session_token,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active,
            'is_expired': self.is_expired()
        }
