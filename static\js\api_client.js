/**
 * API客户端
 * 处理与后端API的通信
 */

class ApiClient {
    constructor() {
        this.baseUrl = '/api';
        this.token = this.getStoredToken();
    }

    /**
     * 获取存储的token
     */
    getStoredToken() {
        return localStorage.getItem('auth_token') || null;
    }

    /**
     * 设置token
     */
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('auth_token', token);
        } else {
            localStorage.removeItem('auth_token');
        }
    }

    /**
     * 获取请求头
     */
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };

        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        return headers;
    }

    /**
     * 发送HTTP请求
     */
    async request(method, url, data = null) {
        const config = {
            method: method,
            headers: this.getHeaders()
        };

        if (data) {
            config.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(`${this.baseUrl}${url}`, config);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `HTTP ${response.status}`);
            }

            return result;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    /**
     * GET请求
     */
    async get(url) {
        return this.request('GET', url);
    }

    /**
     * POST请求
     */
    async post(url, data) {
        return this.request('POST', url, data);
    }

    /**
     * PUT请求
     */
    async put(url, data) {
        return this.request('PUT', url, data);
    }

    /**
     * DELETE请求
     */
    async delete(url) {
        return this.request('DELETE', url);
    }

    // ==================== 认证相关API ====================

    /**
     * 用户注册
     */
    async register(username, password, email = null, displayName = null) {
        const data = {
            username: username,
            password: password,
            email: email,
            display_name: displayName
        };
        return this.post('/auth/register', data);
    }

    /**
     * 用户登录
     */
    async login(username, password) {
        const data = {
            username: username,
            password: password
        };
        const result = await this.post('/auth/login', data);
        
        if (result.token) {
            this.setToken(result.token);
        }
        
        return result;
    }

    /**
     * 用户登出
     */
    async logout() {
        try {
            await this.post('/auth/logout');
        } finally {
            this.setToken(null);
        }
    }

    /**
     * 获取当前用户信息
     */
    async getCurrentUser() {
        return this.get('/auth/me');
    }

    /**
     * 检查认证状态
     */
    async checkAuth() {
        try {
            return await this.get('/auth/check');
        } catch (error) {
            return { authenticated: false };
        }
    }

    // ==================== 对话相关API ====================

    /**
     * 创建新对话
     */
    async createConversation(title, description = null) {
        const data = {
            title: title,
            description: description
        };
        return this.post('/conversations', data);
    }

    /**
     * 获取对话列表
     */
    async getConversations(limit = 50, offset = 0) {
        return this.get(`/conversations?limit=${limit}&offset=${offset}`);
    }

    /**
     * 获取指定对话
     */
    async getConversation(conversationId) {
        return this.get(`/conversations/${conversationId}`);
    }

    /**
     * 更新对话
     */
    async updateConversation(conversationId, title = null, description = null) {
        const data = {};
        if (title !== null) data.title = title;
        if (description !== null) data.description = description;
        
        return this.put(`/conversations/${conversationId}`, data);
    }

    /**
     * 删除对话
     */
    async deleteConversation(conversationId) {
        return this.delete(`/conversations/${conversationId}`);
    }

    /**
     * 添加表单版本
     */
    async addFormVersion(conversationId, formData, versionName = null, createdBy = 'user', modificationNotes = null) {
        const data = {
            form_data: formData,
            version_name: versionName,
            created_by: createdBy,
            modification_notes: modificationNotes
        };
        return this.post(`/conversations/${conversationId}/versions`, data);
    }

    /**
     * 切换版本
     */
    async switchVersion(conversationId, versionId) {
        return this.post(`/conversations/${conversationId}/versions/${versionId}/switch`);
    }

    /**
     * 删除版本
     */
    async deleteVersion(conversationId, versionId) {
        return this.delete(`/conversations/${conversationId}/versions/${versionId}`);
    }

    /**
     * 添加聊天消息
     */
    async addChatMessage(conversationId, sender, message, versionBefore = null, versionAfter = null) {
        const data = {
            sender: sender,
            message: message,
            version_before: versionBefore,
            version_after: versionAfter
        };
        return this.post(`/conversations/${conversationId}/messages`, data);
    }
}

// 全局API客户端实例
window.apiClient = new ApiClient();

// 暴露给其他脚本使用
window.ApiClient = ApiClient;
