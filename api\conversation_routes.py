"""
对话相关的API路由
"""

from flask import Blueprint, request, jsonify
from auth import require_auth, get_current_user
from services import ConversationService
from logger_config import log_user_action, log_error_operation

conversation_bp = Blueprint('conversation', __name__, url_prefix='/api/conversations')

@conversation_bp.route('', methods=['POST'])
@require_auth
def create_conversation():
    """创建新对话"""
    try:
        current_user = get_current_user()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        title = data.get('title', '').strip()
        description = data.get('description', '').strip() or None
        
        if not title:
            return jsonify({'error': '对话标题不能为空'}), 400
        
        conversation, error = ConversationService.create_conversation(
            user_id=current_user.id,
            title=title,
            description=description
        )
        
        if error:
            return jsonify({'error': error}), 400
        
        return jsonify({
            'message': '对话创建成功',
            'conversation': conversation.to_dict()
        }), 201
        
    except Exception as e:
        log_error_operation('create_conversation_api', e)
        return jsonify({'error': '创建对话失败，请稍后重试'}), 500

@conversation_bp.route('', methods=['GET'])
@require_auth
def get_conversations():
    """获取用户的对话列表"""
    try:
        current_user = get_current_user()
        
        # 获取分页参数
        limit = min(int(request.args.get('limit', 50)), 100)  # 最大100条
        offset = int(request.args.get('offset', 0))
        
        conversations, error = ConversationService.get_user_conversations(
            user_id=current_user.id,
            limit=limit,
            offset=offset
        )
        
        if error:
            return jsonify({'error': error}), 400
        
        return jsonify({
            'conversations': [conv.to_dict(include_versions=False, include_messages=False) for conv in conversations],
            'total': len(conversations),
            'limit': limit,
            'offset': offset
        }), 200
        
    except Exception as e:
        log_error_operation('get_conversations_api', e)
        return jsonify({'error': '获取对话列表失败，请稍后重试'}), 500

@conversation_bp.route('/<conversation_id>', methods=['GET'])
@require_auth
def get_conversation(conversation_id):
    """获取指定对话的详细信息"""
    try:
        current_user = get_current_user()
        
        conversation, error = ConversationService.get_conversation(
            conversation_id=conversation_id,
            user_id=current_user.id
        )
        
        if error:
            return jsonify({'error': error}), 400
        
        if not conversation:
            return jsonify({'error': '对话不存在'}), 404
        
        return jsonify({
            'conversation': conversation.to_dict()
        }), 200
        
    except Exception as e:
        log_error_operation('get_conversation_api', e)
        return jsonify({'error': '获取对话失败，请稍后重试'}), 500

@conversation_bp.route('/<conversation_id>', methods=['PUT'])
@require_auth
def update_conversation(conversation_id):
    """更新对话信息"""
    try:
        current_user = get_current_user()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        title = data.get('title')
        description = data.get('description')
        
        conversation, error = ConversationService.update_conversation(
            conversation_id=conversation_id,
            user_id=current_user.id,
            title=title,
            description=description
        )
        
        if error:
            return jsonify({'error': error}), 400
        
        if not conversation:
            return jsonify({'error': '对话不存在'}), 404
        
        return jsonify({
            'message': '对话更新成功',
            'conversation': conversation.to_dict()
        }), 200
        
    except Exception as e:
        log_error_operation('update_conversation_api', e)
        return jsonify({'error': '更新对话失败，请稍后重试'}), 500

@conversation_bp.route('/<conversation_id>', methods=['DELETE'])
@require_auth
def delete_conversation(conversation_id):
    """删除对话"""
    try:
        current_user = get_current_user()
        
        success, error = ConversationService.delete_conversation(
            conversation_id=conversation_id,
            user_id=current_user.id
        )
        
        if error:
            return jsonify({'error': error}), 400
        
        if not success:
            return jsonify({'error': '对话不存在'}), 404
        
        return jsonify({'message': '对话删除成功'}), 200
        
    except Exception as e:
        log_error_operation('delete_conversation_api', e)
        return jsonify({'error': '删除对话失败，请稍后重试'}), 500

@conversation_bp.route('/<conversation_id>/versions', methods=['POST'])
@require_auth
def add_form_version(conversation_id):
    """添加表单版本"""
    try:
        current_user = get_current_user()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        form_data = data.get('form_data', {})
        version_name = data.get('version_name')
        created_by = data.get('created_by', 'user')
        modification_notes = data.get('modification_notes')
        
        version, error = ConversationService.add_form_version(
            conversation_id=conversation_id,
            form_data=form_data,
            version_name=version_name,
            created_by=created_by,
            modification_notes=modification_notes
        )
        
        if error:
            return jsonify({'error': error}), 400
        
        return jsonify({
            'message': '版本创建成功',
            'version': version.to_dict()
        }), 201
        
    except Exception as e:
        log_error_operation('add_form_version_api', e)
        return jsonify({'error': '创建版本失败，请稍后重试'}), 500

@conversation_bp.route('/<conversation_id>/versions/<version_id>/switch', methods=['POST'])
@require_auth
def switch_version(conversation_id, version_id):
    """切换到指定版本"""
    try:
        current_user = get_current_user()
        
        version, error = ConversationService.switch_version(
            conversation_id=conversation_id,
            version_id=version_id
        )
        
        if error:
            return jsonify({'error': error}), 400
        
        return jsonify({
            'message': '版本切换成功',
            'version': version.to_dict()
        }), 200
        
    except Exception as e:
        log_error_operation('switch_version_api', e)
        return jsonify({'error': '切换版本失败，请稍后重试'}), 500

@conversation_bp.route('/<conversation_id>/versions/<version_id>', methods=['DELETE'])
@require_auth
def delete_version(conversation_id, version_id):
    """删除版本"""
    try:
        current_user = get_current_user()
        
        success, error = ConversationService.delete_version(
            conversation_id=conversation_id,
            version_id=version_id
        )
        
        if error:
            return jsonify({'error': error}), 400
        
        if not success:
            return jsonify({'error': '版本不存在或无法删除'}), 404
        
        return jsonify({'message': '版本删除成功'}), 200
        
    except Exception as e:
        log_error_operation('delete_version_api', e)
        return jsonify({'error': '删除版本失败，请稍后重试'}), 500

@conversation_bp.route('/<conversation_id>/messages', methods=['POST'])
@require_auth
def add_chat_message(conversation_id):
    """添加聊天消息"""
    try:
        current_user = get_current_user()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        sender = data.get('sender', 'user')
        message = data.get('message', '').strip()
        version_before = data.get('version_before')
        version_after = data.get('version_after')
        
        if not message:
            return jsonify({'error': '消息内容不能为空'}), 400
        
        chat_message, error = ConversationService.add_chat_message(
            conversation_id=conversation_id,
            sender=sender,
            message=message,
            version_before=version_before,
            version_after=version_after
        )
        
        if error:
            return jsonify({'error': error}), 400
        
        return jsonify({
            'message': '消息添加成功',
            'chat_message': chat_message.to_dict()
        }), 201
        
    except Exception as e:
        log_error_operation('add_chat_message_api', e)
        return jsonify({'error': '添加消息失败，请稍后重试'}), 500
