"""
认证相关的API路由
"""

from flask import Blueprint, request, jsonify, make_response
from auth import register_user, login_user, logout_user, require_auth, get_current_user
from logger_config import log_user_action, log_error_operation

auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        email = data.get('email', '').strip() or None
        display_name = data.get('display_name', '').strip() or None
        
        # 验证必填字段
        if not username:
            return jsonify({'error': '用户名不能为空'}), 400
        
        if not password:
            return jsonify({'error': '密码不能为空'}), 400
        
        if len(password) < 6:
            return jsonify({'error': '密码长度至少6位'}), 400
        
        # 注册用户
        user, error = register_user(username, password, email, display_name)
        
        if error:
            return jsonify({'error': error}), 400
        
        log_user_action('user_register', {
            'user_id': user.id,
            'username': username,
            'email': email
        })
        
        return jsonify({
            'message': '注册成功',
            'user': user.to_dict()
        }), 201
        
    except Exception as e:
        log_error_operation('register_api', e)
        return jsonify({'error': '注册失败，请稍后重试'}), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        
        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400
        
        # 获取客户端信息
        user_agent = request.headers.get('User-Agent')
        ip_address = request.remote_addr
        
        # 登录验证
        user, token, error = login_user(username, password, user_agent, ip_address)
        
        if error:
            return jsonify({'error': error}), 401
        
        log_user_action('user_login', {
            'user_id': user.id,
            'username': username,
            'ip_address': ip_address
        })
        
        # 创建响应
        response = make_response(jsonify({
            'message': '登录成功',
            'user': user.to_dict(),
            'token': token
        }))
        
        # 设置cookie（可选，用于浏览器自动认证）
        response.set_cookie(
            'auth_token',
            token,
            max_age=7*24*60*60,  # 7天
            httponly=True,
            secure=False,  # 在生产环境中应设置为True（需要HTTPS）
            samesite='Lax'
        )
        
        return response, 200
        
    except Exception as e:
        log_error_operation('login_api', e)
        return jsonify({'error': '登录失败，请稍后重试'}), 500

@auth_bp.route('/logout', methods=['POST'])
@require_auth
def logout():
    """用户登出"""
    try:
        # 获取session token（如果有的话）
        session_token = request.cookies.get('session_token')
        
        if session_token:
            logout_user(session_token)
        
        current_user = get_current_user()
        log_user_action('user_logout', {
            'user_id': current_user.id if current_user else None
        })
        
        # 创建响应并清除cookie
        response = make_response(jsonify({'message': '登出成功'}))
        response.set_cookie('auth_token', '', expires=0)
        
        return response, 200
        
    except Exception as e:
        log_error_operation('logout_api', e)
        return jsonify({'error': '登出失败，请稍后重试'}), 500

@auth_bp.route('/me', methods=['GET'])
@require_auth
def get_current_user_info():
    """获取当前用户信息"""
    try:
        current_user = get_current_user()
        
        if not current_user:
            return jsonify({'error': '用户未登录'}), 401
        
        return jsonify({
            'user': current_user.to_dict()
        }), 200
        
    except Exception as e:
        log_error_operation('get_current_user_api', e)
        return jsonify({'error': '获取用户信息失败'}), 500

@auth_bp.route('/check', methods=['GET'])
def check_auth():
    """检查认证状态"""
    try:
        token = None
        
        # 从Authorization header获取token
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
        
        # 从cookie获取token
        if not token:
            token = request.cookies.get('auth_token')
        
        if not token:
            return jsonify({'authenticated': False}), 200
        
        # 验证token
        from auth.auth import auth_manager
        payload = auth_manager.verify_token(token)
        
        if not payload:
            return jsonify({'authenticated': False}), 200
        
        # 获取用户
        from database import User
        user = User.query.get(payload['user_id'])
        
        if not user or not user.is_active:
            return jsonify({'authenticated': False}), 200
        
        return jsonify({
            'authenticated': True,
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        log_error_operation('check_auth_api', e)
        return jsonify({'authenticated': False}), 200
