/**
 * 对话式8D报告管理系统
 * 支持多版本表单、对话历史和智能协作
 * 使用后端API进行数据存储
 */

class ConversationManager {
    constructor() {
        this.currentConversation = null;
        this.activeVersion = null;
        this.conversations = new Map();
        this.listeners = new Map();
        this.isAuthenticated = false;
        this.currentUser = null;

        this.initEventListeners();
        this.checkAuthAndLoadData();
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 监听表单数据变化
        document.addEventListener('DOMContentLoaded', () => {
            this.bindFormEvents();
        });
    }

    /**
     * 检查认证状态并加载数据
     */
    async checkAuthAndLoadData() {
        try {
            const authResult = await window.apiClient.checkAuth();
            this.isAuthenticated = authResult.authenticated;
            this.currentUser = authResult.user || null;

            if (this.isAuthenticated) {
                await this.loadConversationsFromServer();
            } else {
                // 如果未认证，尝试自动登录（使用默认用户）
                await this.autoLogin();
            }
        } catch (error) {
            console.error('检查认证状态失败:', error);
            // 尝试自动登录
            await this.autoLogin();
        }
    }

    /**
     * 自动登录（使用默认用户）
     */
    async autoLogin() {
        try {
            const result = await window.apiClient.login('admin', 'admin123');
            this.isAuthenticated = true;
            this.currentUser = result.user;
            await this.loadConversationsFromServer();
        } catch (error) {
            console.error('自动登录失败:', error);
            this.isAuthenticated = false;
            this.currentUser = null;
        }
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 从服务器加载对话数据
     */
    async loadConversationsFromServer() {
        try {
            if (!this.isAuthenticated) {
                return;
            }

            const result = await window.apiClient.getConversations();
            this.conversations.clear();

            for (const conv of result.conversations) {
                this.conversations.set(conv.conversation_id, conv);
            }

            this.notifyListeners('conversationsLoaded', result.conversations);
        } catch (error) {
            console.error('从服务器加载对话失败:', error);
        }
    }

    /**
     * 生成对话标题
     */
    generateConversationTitle(formData = null) {
        // 如果有D0标题，优先使用
        if (formData && formData.d0_title && formData.d0_title.trim()) {
            return formData.d0_title.trim();
        }

        // 如果有问题描述，使用前20个字符
        if (formData && formData.d2_description && formData.d2_description.trim()) {
            const desc = formData.d2_description.trim().substring(0, 20);
            return `${desc}...`;
        }

        // 生成带序号的默认标题（不包含时间戳）
        // 计算当前已有的对话数量，用于生成序号
        const existingCount = this.conversations.size;
        const sequenceNumber = existingCount + 1;

        return `8D报告 #${sequenceNumber}`;
    }

    /**
     * 生成版本描述
     */
    generateVersionDescription(formData, source, customDescription = null, conversation = null) {
        if (customDescription) {
            return customDescription;
        }

        const timestamp = new Date().toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        if (source === 'user') {
            // 检查是否有实际内容
            const hasContent = this.hasFormContent(formData);
            if (!hasContent) {
                // 为空白版本生成编号，和新建对话一样
                const existingVersionCount = conversation ? conversation.form_versions.length : 0;
                const versionNumber = existingVersionCount + 1;
                return `空白版本 #${versionNumber}`;
            }

            // 如果有D0标题，使用它
            if (formData && formData.d0_title && formData.d0_title.trim()) {
                return `用户输入: ${formData.d0_title.trim().substring(0, 15)}...`;
            }

            // 如果有问题描述，使用它
            if (formData && formData.d2_description && formData.d2_description.trim()) {
                return `用户输入: ${formData.d2_description.trim().substring(0, 15)}...`;
            }

            return `用户输入`;
        } else if (source === 'ai') {
            return `AI优化版本`;
        } else {
            return `${source}版本`;
        }
    }

    /**
     * 检查表单是否有实际内容
     */
    hasFormContent(formData) {
        if (!formData || typeof formData !== 'object') {
            return false;
        }

        for (const key in formData) {
            const value = formData[key];
            if (value && typeof value === 'string' && value.trim() !== '') {
                return true;
            } else if (value && typeof value === 'object') {
                // 递归检查嵌套对象
                if (this.hasFormContent(value)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 创建新对话
     */
    async createConversation(initialFormData = null, title = null) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('用户未登录');
            }

            const conversationTitle = title || this.generateConversationTitle(initialFormData);
            const result = await window.apiClient.createConversation(conversationTitle);
            const conversation = result.conversation;

            // 如果有初始数据，创建第一个版本
            if (initialFormData && !this.isFormEmpty(initialFormData)) {
                await this.addFormVersion(conversation, initialFormData, 'user');
                // 重新获取对话数据（包含新版本）
                const updatedResult = await window.apiClient.getConversation(conversation.conversation_id);
                Object.assign(conversation, updatedResult.conversation);
            }

            // 添加到内存中
            this.conversations.set(conversation.conversation_id, conversation);
            this.currentConversation = conversation;

            this.notifyListeners('conversationCreated', conversation);
            return conversation.conversation_id;
        } catch (error) {
            console.error('创建对话失败:', error);
            throw error;
        }
    }

    /**
     * 添加表单版本
     */
    async addFormVersion(conversation, formData, createdBy = 'user', customName = null, modificationNotes = null) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('用户未登录');
            }

            const versionName = customName || this.generateVersionDescription(formData, createdBy, null, conversation);

            const result = await window.apiClient.addFormVersion(
                conversation.conversation_id,
                formData,
                versionName,
                createdBy,
                modificationNotes
            );

            const version = result.version;

            // 更新本地对话数据
            conversation.form_versions.forEach(v => v.is_active = false);
            conversation.form_versions.unshift(version);
            conversation.updated_at = new Date().toISOString();
            this.activeVersion = version.version_id;

            // 更新内存中的对话
            this.conversations.set(conversation.conversation_id, conversation);

            this.notifyListeners('versionAdded', { conversation, version });

            return version.version_id;
        } catch (error) {
            console.error('添加版本失败:', error);
            throw error;
        }
    }

    /**
     * 切换到指定版本
     */
    switchToVersion(versionId) {
        if (!this.currentConversation) return false;

        const version = this.currentConversation.form_versions.find(v => v.version_id === versionId);
        if (!version) return false;

        // 设置所有版本为非活跃
        this.currentConversation.form_versions.forEach(v => v.is_active = false);
        version.is_active = true;
        this.activeVersion = versionId;

        this.notifyListeners('versionSwitched', { conversation: this.currentConversation, version });
        
        return true;
    }

    /**
     * 获取当前活跃版本
     */
    getActiveVersion() {
        if (!this.currentConversation) return null;
        return this.currentConversation.form_versions.find(v => v.is_active);
    }

    /**
     * 删除版本
     */
    deleteVersion(versionId) {
        if (!this.currentConversation) return false;

        const versionIndex = this.currentConversation.form_versions.findIndex(v => v.version_id === versionId);
        if (versionIndex === -1) return false;

        // 不能删除唯一的版本
        if (this.currentConversation.form_versions.length <= 1) {
            return false;
        }

        const deletedVersion = this.currentConversation.form_versions[versionIndex];
        const wasActive = deletedVersion.is_active;

        // 删除版本
        this.currentConversation.form_versions.splice(versionIndex, 1);

        // 如果删除的是活跃版本，需要激活另一个版本
        if (wasActive && this.currentConversation.form_versions.length > 0) {
            // 激活最后一个版本
            const lastVersion = this.currentConversation.form_versions[this.currentConversation.form_versions.length - 1];
            lastVersion.is_active = true;
            this.activeVersion = lastVersion.version_id;
        }

        this.currentConversation.updated_at = new Date().toISOString();
        this.notifyListeners('versionDeleted', {
            conversation: this.currentConversation, 
            deletedVersionId: versionId,
            newActiveVersion: wasActive ? this.getActiveVersion() : null
        });
        
        // 删除版本成功
        return true;
    }

    /**
     * 添加聊天消息
     */
    addChatMessage(sender, message, versionBefore = null, versionAfter = null) {
        if (!this.currentConversation) return null;

        const messageId = this.generateId();
        const chatMessage = {
            message_id: messageId,
            sender: sender, // 'user' 或 'ai'
            message: message,
            timestamp: new Date().toISOString(),
            version_before: versionBefore,
            version_after: versionAfter
        };

        this.currentConversation.chat_history.push(chatMessage);
        this.currentConversation.updated_at = new Date().toISOString();

        this.notifyListeners('messageAdded', { conversation: this.currentConversation, message: chatMessage });
        
        return messageId;
    }

    /**
     * 加载对话
     */
    loadConversation(conversationId) {
        const conversation = this.conversations.get(conversationId);
        if (!conversation) return false;

        this.currentConversation = conversation;
        
        // 找到活跃版本
        const activeVersion = conversation.form_versions.find(v => v.is_active);
        if (activeVersion) {
            this.activeVersion = activeVersion.version_id;
        } else if (conversation.form_versions.length > 0) {
            // 如果没有活跃版本，激活最后一个版本
            const lastVersion = conversation.form_versions[conversation.form_versions.length - 1];
            lastVersion.is_active = true;
            this.activeVersion = lastVersion.version_id;
        }

        this.notifyListeners('conversationLoaded', conversation);
        return true;
    }

    /**
     * 删除对话
     */
    deleteConversation(conversationId) {
        const conversation = this.conversations.get(conversationId);
        if (!conversation) return false;

        this.conversations.delete(conversationId);
        
        if (this.currentConversation && this.currentConversation.conversation_id === conversationId) {
            this.currentConversation = null;
            this.activeVersion = null;
        }

        this.notifyListeners('conversationDeleted', conversationId);
        return true;
    }

    /**
     * 获取所有对话列表
     */
    getAllConversations() {
        return Array.from(this.conversations.values()).sort((a, b) => 
            new Date(b.updated_at) - new Date(a.updated_at)
        );
    }

    /**
     * 检查表单是否为空
     */
    isFormEmpty(formData) {
        if (!formData || typeof formData !== 'object') return true;
        
        for (const key in formData) {
            if (formData[key] && String(formData[key]).trim() !== '') {
                return false;
            }
        }
        return true;
    }



    /**
     * 清理过期对话
     */
    cleanupExpiredConversations(daysToKeep = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

        let deletedCount = 0;
        this.conversations.forEach((conversation, id) => {
            if (new Date(conversation.updated_at) < cutoffDate) {
                this.conversations.delete(id);
                deletedCount++;
            }
        });

        if (deletedCount > 0) {
            // 清理完成，已删除过期对话
        }
    }

    /**
     * 添加事件监听器
     */
    addEventListener(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(event, callback) {
        if (!this.listeners.has(event)) return;
        const callbacks = this.listeners.get(event);
        const index = callbacks.indexOf(callback);
        if (index > -1) {
            callbacks.splice(index, 1);
        }
    }

    /**
     * 通知监听器
     */
    notifyListeners(event, data) {
        if (!this.listeners.has(event)) return;
        this.listeners.get(event).forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                // 静默处理错误
            }
        });
    }

    /**
     * 绑定表单事件
     */
    bindFormEvents() {
        // 这里将在后续实现中绑定表单变化事件

    }



    /**
     * 导入对话数据
     */
    importConversation(conversationData) {
        try {
            // 生成新的ID以避免冲突
            const newId = this.generateId();
            const conversation = {
                ...conversationData,
                conversation_id: newId,
                imported_at: new Date().toISOString()
            };

            this.conversations.set(newId, conversation);
            
            return newId;
        } catch (error) {
            return null;
        }
    }
}

// 全局对话管理器实例
window.conversationManager = new ConversationManager();

// 暴露给其他脚本使用
window.ConversationManager = ConversationManager; 