"""
数据库连接和初始化模块
"""

import os
import sqlite3
from flask import current_app
from .models import db, User, Conversation, FormVersion, ChatMessage, UserSession

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化Flask应用"""
        # 配置数据库
        basedir = os.path.abspath(os.path.dirname(__file__))
        database_path = os.path.join(basedir, '..', 'data', 'app.db')
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(database_path), exist_ok=True)
        
        app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{database_path}'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
            'pool_pre_ping': True,
            'pool_recycle': 300,
        }
        
        # 初始化数据库
        db.init_app(app)
        
        # 注册CLI命令
        app.cli.add_command(init_db_command)
        app.cli.add_command(reset_db_command)
    
    def create_tables(self):
        """创建所有表"""
        with current_app.app_context():
            db.create_all()
            current_app.logger.info("数据库表创建完成")
    
    def drop_tables(self):
        """删除所有表"""
        with current_app.app_context():
            db.drop_all()
            current_app.logger.info("数据库表删除完成")
    
    def reset_database(self):
        """重置数据库"""
        self.drop_tables()
        self.create_tables()
        current_app.logger.info("数据库重置完成")
    
    def get_db_info(self):
        """获取数据库信息"""
        try:
            with current_app.app_context():
                # 获取表信息
                inspector = db.inspect(db.engine)
                tables = inspector.get_table_names()
                
                info = {
                    'database_path': current_app.config['SQLALCHEMY_DATABASE_URI'],
                    'tables': tables,
                    'table_counts': {}
                }
                
                # 获取每个表的记录数
                for table in tables:
                    try:
                        if table == 'users':
                            count = User.query.count()
                        elif table == 'conversations':
                            count = Conversation.query.count()
                        elif table == 'form_versions':
                            count = FormVersion.query.count()
                        elif table == 'chat_messages':
                            count = ChatMessage.query.count()
                        elif table == 'user_sessions':
                            count = UserSession.query.count()
                        else:
                            count = 0
                        info['table_counts'][table] = count
                    except Exception as e:
                        info['table_counts'][table] = f"Error: {str(e)}"
                
                return info
        except Exception as e:
            return {'error': str(e)}

# 全局数据库管理器实例
database_manager = DatabaseManager()

def init_db():
    """初始化数据库"""
    database_manager.create_tables()

def reset_db():
    """重置数据库"""
    database_manager.reset_database()

# CLI命令
import click

@click.command('init-db')
def init_db_command():
    """初始化数据库的CLI命令"""
    init_db()
    click.echo('数据库初始化完成。')

@click.command('reset-db')
def reset_db_command():
    """重置数据库的CLI命令"""
    if click.confirm('确定要重置数据库吗？这将删除所有数据！'):
        reset_db()
        click.echo('数据库重置完成。')
    else:
        click.echo('操作已取消。')

# 数据库操作辅助函数
def get_or_create_user(username, email=None, password=None, display_name=None):
    """获取或创建用户"""
    user = User.query.filter_by(username=username).first()
    if not user and password:
        user = User(
            username=username,
            email=email,
            display_name=display_name or username
        )
        user.set_password(password)
        db.session.add(user)
        db.session.commit()
    return user

def create_conversation(user_id, title, description=None):
    """创建新对话"""
    import uuid
    conversation_id = str(uuid.uuid4())
    
    conversation = Conversation(
        conversation_id=conversation_id,
        user_id=user_id,
        title=title,
        description=description
    )
    
    db.session.add(conversation)
    db.session.commit()
    return conversation

def create_form_version(conversation_id, form_data, version_name=None, created_by='user', modification_notes=None):
    """创建表单版本"""
    import uuid
    from datetime import datetime
    
    # 获取对话
    conversation = Conversation.query.filter_by(conversation_id=conversation_id).first()
    if not conversation:
        raise ValueError(f"对话 {conversation_id} 不存在")
    
    # 生成版本ID和名称
    version_id = f"v{int(datetime.now().timestamp() * 1000)}"
    if not version_name:
        version_count = conversation.form_versions.count()
        version_name = f"版本 {version_count + 1}"
    
    # 设置所有版本为非活跃
    FormVersion.query.filter_by(conversation_id=conversation.id).update({'is_active': False})
    
    # 创建新版本
    version = FormVersion(
        version_id=version_id,
        conversation_id=conversation.id,
        version_name=version_name,
        created_by=created_by,
        modification_notes=modification_notes,
        is_active=True
    )
    version.set_form_data(form_data)
    
    db.session.add(version)
    db.session.commit()
    return version

def create_chat_message(conversation_id, sender, message, version_before=None, version_after=None):
    """创建聊天消息"""
    import uuid
    
    # 获取对话
    conversation = Conversation.query.filter_by(conversation_id=conversation_id).first()
    if not conversation:
        raise ValueError(f"对话 {conversation_id} 不存在")
    
    message_id = str(uuid.uuid4())
    
    chat_message = ChatMessage(
        message_id=message_id,
        conversation_id=conversation.id,
        sender=sender,
        message=message,
        version_before=version_before,
        version_after=version_after
    )
    
    db.session.add(chat_message)
    db.session.commit()
    return chat_message
