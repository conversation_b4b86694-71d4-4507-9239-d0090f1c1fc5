"""
简单测试：验证基本的SQLite数据库操作
"""

import sqlite3
import json
import os
from datetime import datetime

def create_database():
    """创建SQLite数据库和表"""
    # 确保data目录存在
    os.makedirs('data', exist_ok=True)
    
    # 连接数据库
    conn = sqlite3.connect('data/app.db')
    cursor = conn.cursor()
    
    # 创建用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE,
            password_hash TEXT NOT NULL,
            display_name TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
    ''')
    
    # 创建对话表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS conversations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            conversation_id TEXT UNIQUE NOT NULL,
            user_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # 创建表单版本表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS form_versions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version_id TEXT UNIQUE NOT NULL,
            conversation_id INTEGER NOT NULL,
            version_name TEXT NOT NULL,
            form_data TEXT NOT NULL,
            created_by TEXT DEFAULT 'user',
            modification_notes TEXT,
            is_active BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (conversation_id) REFERENCES conversations (id)
        )
    ''')
    
    # 创建聊天消息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            message_id TEXT UNIQUE NOT NULL,
            conversation_id INTEGER NOT NULL,
            sender TEXT NOT NULL,
            message TEXT NOT NULL,
            version_before TEXT,
            version_after TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (conversation_id) REFERENCES conversations (id)
        )
    ''')
    
    conn.commit()
    conn.close()
    print("数据库和表创建成功")

def test_user_operations():
    """测试用户操作"""
    conn = sqlite3.connect('data/app.db')
    cursor = conn.cursor()
    
    # 插入测试用户
    try:
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, display_name)
            VALUES (?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', 'hashed_password_123', '管理员'))
        
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, display_name)
            VALUES (?, ?, ?, ?)
        ''', ('testuser', '<EMAIL>', 'hashed_password_456', '测试用户'))
        
        conn.commit()
        print("用户创建成功")
    except sqlite3.IntegrityError as e:
        print(f"用户可能已存在: {e}")
    
    # 查询用户
    cursor.execute('SELECT * FROM users')
    users = cursor.fetchall()
    print(f"用户列表: {users}")
    
    conn.close()

def test_conversation_operations():
    """测试对话操作"""
    conn = sqlite3.connect('data/app.db')
    cursor = conn.cursor()
    
    # 获取用户ID
    cursor.execute('SELECT id FROM users WHERE username = ?', ('admin',))
    user_result = cursor.fetchone()
    if not user_result:
        print("未找到admin用户")
        conn.close()
        return
    
    user_id = user_result[0]
    
    # 创建对话
    conversation_id = f"conv_{int(datetime.now().timestamp())}"
    cursor.execute('''
        INSERT INTO conversations (conversation_id, user_id, title, description)
        VALUES (?, ?, ?, ?)
    ''', (conversation_id, user_id, '测试8D报告', '这是一个测试对话'))
    
    # 获取对话的数据库ID
    conv_db_id = cursor.lastrowid
    
    # 创建表单版本
    form_data = {
        'd0_title': '测试问题',
        'd2_description': '这是一个测试问题的描述',
        'd4_why1': '为什么会发生这个问题？'
    }
    
    version_id = f"v{int(datetime.now().timestamp() * 1000)}"
    cursor.execute('''
        INSERT INTO form_versions (version_id, conversation_id, version_name, form_data, is_active)
        VALUES (?, ?, ?, ?, ?)
    ''', (version_id, conv_db_id, '初始版本', json.dumps(form_data, ensure_ascii=False), 1))
    
    # 创建聊天消息
    message_id = f"msg_{int(datetime.now().timestamp())}"
    cursor.execute('''
        INSERT INTO chat_messages (message_id, conversation_id, sender, message)
        VALUES (?, ?, ?, ?)
    ''', (message_id, conv_db_id, 'user', '这是一条测试消息'))
    
    conn.commit()
    print("对话、版本和消息创建成功")
    
    # 查询数据
    cursor.execute('''
        SELECT c.conversation_id, c.title, c.description, c.created_at,
               COUNT(fv.id) as version_count,
               COUNT(cm.id) as message_count
        FROM conversations c
        LEFT JOIN form_versions fv ON c.id = fv.conversation_id
        LEFT JOIN chat_messages cm ON c.id = cm.conversation_id
        WHERE c.user_id = ?
        GROUP BY c.id
    ''', (user_id,))
    
    conversations = cursor.fetchall()
    print(f"用户对话: {conversations}")
    
    conn.close()

def test_database_info():
    """获取数据库信息"""
    conn = sqlite3.connect('data/app.db')
    cursor = conn.cursor()
    
    # 获取表列表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"数据库表: {[table[0] for table in tables]}")
    
    # 获取每个表的记录数
    for table in tables:
        table_name = table[0]
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"表 {table_name}: {count} 条记录")
    
    conn.close()

if __name__ == '__main__':
    print("开始测试SQLite数据库操作...")
    
    # 创建数据库
    create_database()
    
    # 测试用户操作
    test_user_operations()
    
    # 测试对话操作
    test_conversation_operations()
    
    # 显示数据库信息
    test_database_info()
    
    print("测试完成！")
