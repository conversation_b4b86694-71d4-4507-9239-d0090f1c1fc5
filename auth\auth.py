"""
用户认证模块
包含用户注册、登录、会话管理等功能
"""

import os
import jwt
import uuid
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, current_app, g
from database import db, User, UserSession

class AuthManager:
    """认证管理器"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化Flask应用"""
        # 设置JWT密钥
        app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
        app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(days=7)  # 7天过期
        
    def generate_token(self, user_id):
        """生成JWT token"""
        payload = {
            'user_id': user_id,
            'exp': datetime.utcnow() + current_app.config['JWT_ACCESS_TOKEN_EXPIRES'],
            'iat': datetime.utcnow(),
            'jti': str(uuid.uuid4())  # JWT ID
        }
        
        token = jwt.encode(
            payload,
            current_app.config['JWT_SECRET_KEY'],
            algorithm='HS256'
        )
        
        return token
    
    def verify_token(self, token):
        """验证JWT token"""
        try:
            payload = jwt.decode(
                token,
                current_app.config['JWT_SECRET_KEY'],
                algorithms=['HS256']
            )
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    def create_session(self, user_id, user_agent=None, ip_address=None):
        """创建用户会话"""
        session_token = str(uuid.uuid4())
        expires_at = datetime.utcnow() + current_app.config['JWT_ACCESS_TOKEN_EXPIRES']
        
        session = UserSession(
            session_token=session_token,
            user_id=user_id,
            expires_at=expires_at,
            user_agent=user_agent,
            ip_address=ip_address
        )
        
        db.session.add(session)
        db.session.commit()
        
        return session
    
    def get_session(self, session_token):
        """获取会话"""
        return UserSession.query.filter_by(
            session_token=session_token,
            is_active=True
        ).first()
    
    def invalidate_session(self, session_token):
        """使会话失效"""
        session = self.get_session(session_token)
        if session:
            session.is_active = False
            db.session.commit()
        return session is not None
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        expired_sessions = UserSession.query.filter(
            UserSession.expires_at < datetime.utcnow()
        ).all()
        
        for session in expired_sessions:
            session.is_active = False
        
        db.session.commit()
        return len(expired_sessions)

# 全局认证管理器实例
auth_manager = AuthManager()

def register_user(username, password, email=None, display_name=None):
    """注册新用户"""
    # 检查用户名是否已存在
    existing_user = User.query.filter_by(username=username).first()
    if existing_user:
        return None, "用户名已存在"
    
    # 检查邮箱是否已存在
    if email:
        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            return None, "邮箱已被使用"
    
    # 创建新用户
    user = User(
        username=username,
        email=email,
        display_name=display_name or username
    )
    user.set_password(password)
    
    try:
        db.session.add(user)
        db.session.commit()
        return user, None
    except Exception as e:
        db.session.rollback()
        return None, f"注册失败: {str(e)}"

def login_user(username, password, user_agent=None, ip_address=None):
    """用户登录"""
    user = User.query.filter_by(username=username, is_active=True).first()
    
    if not user or not user.check_password(password):
        return None, None, "用户名或密码错误"
    
    # 更新最后登录时间
    user.last_login = datetime.utcnow()
    
    # 创建会话
    session = auth_manager.create_session(user.id, user_agent, ip_address)
    
    # 生成JWT token
    token = auth_manager.generate_token(user.id)
    
    db.session.commit()
    
    return user, token, None

def logout_user(session_token):
    """用户登出"""
    return auth_manager.invalidate_session(session_token)

def get_current_user():
    """获取当前用户"""
    return getattr(g, 'current_user', None)

def require_auth(f):
    """需要认证的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        
        # 从Authorization header获取token
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
        
        # 从cookie获取token
        if not token:
            token = request.cookies.get('auth_token')
        
        if not token:
            return jsonify({'error': '缺少认证token'}), 401
        
        # 验证token
        payload = auth_manager.verify_token(token)
        if not payload:
            return jsonify({'error': '无效或过期的token'}), 401
        
        # 获取用户
        user = User.query.get(payload['user_id'])
        if not user or not user.is_active:
            return jsonify({'error': '用户不存在或已被禁用'}), 401
        
        # 设置当前用户
        g.current_user = user
        
        return f(*args, **kwargs)
    
    return decorated_function

def optional_auth(f):
    """可选认证的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        
        # 从Authorization header获取token
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
        
        # 从cookie获取token
        if not token:
            token = request.cookies.get('auth_token')
        
        if token:
            # 验证token
            payload = auth_manager.verify_token(token)
            if payload:
                # 获取用户
                user = User.query.get(payload['user_id'])
                if user and user.is_active:
                    g.current_user = user
        
        # 如果没有token或验证失败，current_user为None
        if not hasattr(g, 'current_user'):
            g.current_user = None
        
        return f(*args, **kwargs)
    
    return decorated_function

def create_default_user():
    """创建默认用户（用于开发和测试）"""
    default_username = 'admin'
    default_password = 'admin123'
    
    existing_user = User.query.filter_by(username=default_username).first()
    if not existing_user:
        user, error = register_user(
            username=default_username,
            password=default_password,
            email='<EMAIL>',
            display_name='管理员'
        )
        if user:
            current_app.logger.info(f"创建默认用户: {default_username}")
            return user
        else:
            current_app.logger.error(f"创建默认用户失败: {error}")
    
    return existing_user
