"""
对话服务层
处理对话相关的业务逻辑
"""

import uuid
from datetime import datetime
from database import db, Conversation, FormVersion, ChatMessage, User
from logger_config import log_business_operation, log_error_operation

class ConversationService:
    """对话服务"""
    
    @staticmethod
    def create_conversation(user_id, title, description=None):
        """创建新对话"""
        try:
            conversation_id = str(uuid.uuid4())
            
            conversation = Conversation(
                conversation_id=conversation_id,
                user_id=user_id,
                title=title,
                description=description
            )
            
            db.session.add(conversation)
            db.session.commit()
            
            log_business_operation('create_conversation', {
                'conversation_id': conversation_id,
                'user_id': user_id,
                'title': title
            })
            
            return conversation, None
        except Exception as e:
            db.session.rollback()
            log_error_operation('create_conversation', e, {'user_id': user_id, 'title': title})
            return None, str(e)
    
    @staticmethod
    def get_conversation(conversation_id, user_id=None):
        """获取对话"""
        try:
            query = Conversation.query.filter_by(conversation_id=conversation_id, is_active=True)
            if user_id:
                query = query.filter_by(user_id=user_id)
            
            conversation = query.first()
            return conversation, None
        except Exception as e:
            log_error_operation('get_conversation', e, {'conversation_id': conversation_id, 'user_id': user_id})
            return None, str(e)
    
    @staticmethod
    def get_user_conversations(user_id, limit=50, offset=0):
        """获取用户的对话列表"""
        try:
            conversations = Conversation.query.filter_by(
                user_id=user_id,
                is_active=True
            ).order_by(
                Conversation.updated_at.desc()
            ).limit(limit).offset(offset).all()
            
            return conversations, None
        except Exception as e:
            log_error_operation('get_user_conversations', e, {'user_id': user_id})
            return None, str(e)
    
    @staticmethod
    def update_conversation(conversation_id, user_id, title=None, description=None):
        """更新对话"""
        try:
            conversation = Conversation.query.filter_by(
                conversation_id=conversation_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            if not conversation:
                return None, "对话不存在"
            
            if title is not None:
                conversation.title = title
            if description is not None:
                conversation.description = description
            
            conversation.updated_at = datetime.utcnow()
            db.session.commit()
            
            log_business_operation('update_conversation', {
                'conversation_id': conversation_id,
                'user_id': user_id,
                'title': title,
                'description': description
            })
            
            return conversation, None
        except Exception as e:
            db.session.rollback()
            log_error_operation('update_conversation', e, {
                'conversation_id': conversation_id,
                'user_id': user_id
            })
            return None, str(e)
    
    @staticmethod
    def delete_conversation(conversation_id, user_id):
        """删除对话（软删除）"""
        try:
            conversation = Conversation.query.filter_by(
                conversation_id=conversation_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            if not conversation:
                return False, "对话不存在"
            
            conversation.is_active = False
            conversation.updated_at = datetime.utcnow()
            db.session.commit()
            
            log_business_operation('delete_conversation', {
                'conversation_id': conversation_id,
                'user_id': user_id
            })
            
            return True, None
        except Exception as e:
            db.session.rollback()
            log_error_operation('delete_conversation', e, {
                'conversation_id': conversation_id,
                'user_id': user_id
            })
            return False, str(e)
    
    @staticmethod
    def add_form_version(conversation_id, form_data, version_name=None, created_by='user', modification_notes=None):
        """添加表单版本"""
        try:
            conversation = Conversation.query.filter_by(
                conversation_id=conversation_id,
                is_active=True
            ).first()
            
            if not conversation:
                return None, "对话不存在"
            
            # 生成版本ID和名称
            version_id = f"v{int(datetime.now().timestamp() * 1000)}"
            if not version_name:
                version_count = conversation.form_versions.count()
                version_name = f"版本 {version_count + 1}"
            
            # 设置所有版本为非活跃
            FormVersion.query.filter_by(conversation_id=conversation.id).update({'is_active': False})
            
            # 创建新版本
            version = FormVersion(
                version_id=version_id,
                conversation_id=conversation.id,
                version_name=version_name,
                created_by=created_by,
                modification_notes=modification_notes,
                is_active=True
            )
            version.set_form_data(form_data)
            
            # 更新对话的更新时间
            conversation.updated_at = datetime.utcnow()
            
            db.session.add(version)
            db.session.commit()
            
            log_business_operation('add_form_version', {
                'conversation_id': conversation_id,
                'version_id': version_id,
                'created_by': created_by
            })
            
            return version, None
        except Exception as e:
            db.session.rollback()
            log_error_operation('add_form_version', e, {
                'conversation_id': conversation_id,
                'created_by': created_by
            })
            return None, str(e)
    
    @staticmethod
    def switch_version(conversation_id, version_id):
        """切换到指定版本"""
        try:
            conversation = Conversation.query.filter_by(
                conversation_id=conversation_id,
                is_active=True
            ).first()
            
            if not conversation:
                return None, "对话不存在"
            
            # 查找指定版本
            version = FormVersion.query.filter_by(
                version_id=version_id,
                conversation_id=conversation.id
            ).first()
            
            if not version:
                return None, "版本不存在"
            
            # 设置所有版本为非活跃
            FormVersion.query.filter_by(conversation_id=conversation.id).update({'is_active': False})
            
            # 激活指定版本
            version.is_active = True
            conversation.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            log_business_operation('switch_version', {
                'conversation_id': conversation_id,
                'version_id': version_id
            })
            
            return version, None
        except Exception as e:
            db.session.rollback()
            log_error_operation('switch_version', e, {
                'conversation_id': conversation_id,
                'version_id': version_id
            })
            return None, str(e)
    
    @staticmethod
    def delete_version(conversation_id, version_id):
        """删除版本"""
        try:
            conversation = Conversation.query.filter_by(
                conversation_id=conversation_id,
                is_active=True
            ).first()
            
            if not conversation:
                return False, "对话不存在"
            
            # 检查版本数量
            version_count = conversation.form_versions.count()
            if version_count <= 1:
                return False, "不能删除唯一的版本"
            
            # 查找要删除的版本
            version = FormVersion.query.filter_by(
                version_id=version_id,
                conversation_id=conversation.id
            ).first()
            
            if not version:
                return False, "版本不存在"
            
            was_active = version.is_active
            
            # 删除版本
            db.session.delete(version)
            
            # 如果删除的是活跃版本，激活最新的版本
            if was_active:
                latest_version = FormVersion.query.filter_by(
                    conversation_id=conversation.id
                ).order_by(FormVersion.created_at.desc()).first()
                
                if latest_version:
                    latest_version.is_active = True
            
            conversation.updated_at = datetime.utcnow()
            db.session.commit()
            
            log_business_operation('delete_version', {
                'conversation_id': conversation_id,
                'version_id': version_id,
                'was_active': was_active
            })
            
            return True, None
        except Exception as e:
            db.session.rollback()
            log_error_operation('delete_version', e, {
                'conversation_id': conversation_id,
                'version_id': version_id
            })
            return False, str(e)
    
    @staticmethod
    def add_chat_message(conversation_id, sender, message, version_before=None, version_after=None):
        """添加聊天消息"""
        try:
            conversation = Conversation.query.filter_by(
                conversation_id=conversation_id,
                is_active=True
            ).first()
            
            if not conversation:
                return None, "对话不存在"
            
            message_id = str(uuid.uuid4())
            
            chat_message = ChatMessage(
                message_id=message_id,
                conversation_id=conversation.id,
                sender=sender,
                message=message,
                version_before=version_before,
                version_after=version_after
            )
            
            conversation.updated_at = datetime.utcnow()
            
            db.session.add(chat_message)
            db.session.commit()
            
            log_business_operation('add_chat_message', {
                'conversation_id': conversation_id,
                'message_id': message_id,
                'sender': sender
            })
            
            return chat_message, None
        except Exception as e:
            db.session.rollback()
            log_error_operation('add_chat_message', e, {
                'conversation_id': conversation_id,
                'sender': sender
            })
            return None, str(e)
