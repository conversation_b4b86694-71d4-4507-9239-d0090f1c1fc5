"""
简化的测试应用，用于验证数据库功能
"""

from flask import Flask, jsonify
import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from database import database_manager, db, User, Conversation, FormVersion, ChatMessage
    from auth import auth_manager, create_default_user, register_user, login_user
    from api import register_blueprints
    
    app = Flask(__name__)
    app.secret_key = 'test-secret-key'
    
    # 初始化数据库和认证
    database_manager.init_app(app)
    auth_manager.init_app(app)
    
    # 注册API蓝图
    register_blueprints(app)
    
    @app.route('/test')
    def test():
        """测试端点"""
        return jsonify({
            'status': 'success',
            'message': '数据库系统运行正常',
            'database_info': database_manager.get_db_info()
        })
    
    @app.route('/test/create-user')
    def test_create_user():
        """测试创建用户"""
        try:
            user, error = register_user('testuser', 'password123', '<EMAIL>', '测试用户')
            if error:
                return jsonify({'error': error}), 400
            
            return jsonify({
                'status': 'success',
                'message': '用户创建成功',
                'user': user.to_dict()
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/test/login')
    def test_login():
        """测试用户登录"""
        try:
            user, token, error = login_user('admin', 'admin123')
            if error:
                return jsonify({'error': error}), 400
            
            return jsonify({
                'status': 'success',
                'message': '登录成功',
                'user': user.to_dict(),
                'token': token
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    def initialize_app():
        """初始化应用"""
        try:
            with app.app_context():
                # 创建数据库表
                db.create_all()
                print("数据库表创建完成")
                
                # 创建默认用户
                create_default_user()
                print("默认用户创建完成")
                
                print("应用初始化完成")
        except Exception as e:
            print(f"应用初始化失败: {e}")
            import traceback
            traceback.print_exc()
    
    if __name__ == '__main__':
        initialize_app()
        print("启动测试应用...")
        app.run(host='0.0.0.0', port=5556, debug=True)

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所需的依赖包")
    
    # 创建一个简单的Flask应用作为备用
    app = Flask(__name__)
    
    @app.route('/')
    def index():
        return jsonify({
            'error': '数据库模块导入失败',
            'message': '请安装flask-sqlalchemy等依赖包',
            'import_error': str(e)
        })
    
    if __name__ == '__main__':
        app.run(host='0.0.0.0', port=5556, debug=True)
